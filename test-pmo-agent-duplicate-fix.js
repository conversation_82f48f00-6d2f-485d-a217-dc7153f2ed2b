/**
 * Test script to verify PMO Agent duplicate creation fix
 * This script tests the agent existence checking and reuse logic
 */

const { checkPMOAgentExists } = require('./lib/elevenlabs/agentValidation');
const { generatePMOAgentId } = require('./lib/agents/voice/pmoAgentVoiceConfig');

async function testPMOAgentDuplicateFix() {
  console.log('🧪 Testing PMO Agent Duplicate Creation Fix');
  console.log('=' .repeat(50));

  const testUserId = '<EMAIL>';
  const testAgentType = 'InvestigativeResearch';
  const testAgentId = generatePMOAgentId(testUserId, testAgentType);

  console.log(`📋 Test Parameters:`);
  console.log(`   User ID: ${testUserId}`);
  console.log(`   Agent Type: ${testAgentType}`);
  console.log(`   Generated Agent ID: ${testAgentId}`);
  console.log('');

  try {
    // Test 1: Check if agent exists (should return false for new agent)
    console.log('🔍 Test 1: Checking if agent exists (should be false for new test)');
    const existenceCheck1 = await checkPMOAgentExists(testUserId, testAgentId);
    
    console.log(`   Result: ${JSON.stringify(existenceCheck1, null, 2)}`);
    console.log(`   ✅ Agent existence check completed`);
    console.log('');

    // Test 2: Simulate agent creation by calling the API
    console.log('🚀 Test 2: Simulating agent creation via API');
    
    const createAgentResponse = await fetch('http://localhost:3000/api/elevenlabs/create-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentId: testAgentId,
        name: 'Test Investigative Researcher',
        voiceId: 'iiidtqDt9FBdT1vfBluA',
        prompt: 'You are a test investigative researcher agent.',
        knowledgeBase: []
      })
    });

    if (createAgentResponse.ok) {
      const createResult = await createAgentResponse.json();
      console.log(`   Creation Result: ${JSON.stringify(createResult, null, 2)}`);
      
      if (createResult.success) {
        console.log(`   ✅ Agent creation successful`);
        console.log(`   📝 Was Existing: ${createResult.wasExisting}`);
      } else {
        console.log(`   ❌ Agent creation failed: ${createResult.error}`);
      }
    } else {
      console.log(`   ❌ API call failed: ${createAgentResponse.status} ${createAgentResponse.statusText}`);
    }
    console.log('');

    // Test 3: Check if agent exists again (should return true now)
    console.log('🔍 Test 3: Checking if agent exists after creation (should be true)');
    const existenceCheck2 = await checkPMOAgentExists(testUserId, testAgentId);
    
    console.log(`   Result: ${JSON.stringify(existenceCheck2, null, 2)}`);
    console.log(`   ✅ Second existence check completed`);
    console.log('');

    // Test 4: Try to create the same agent again (should reuse existing)
    console.log('🔄 Test 4: Attempting to create same agent again (should reuse existing)');
    
    const createAgentResponse2 = await fetch('http://localhost:3000/api/elevenlabs/create-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentId: testAgentId,
        name: 'Test Investigative Researcher',
        voiceId: 'iiidtqDt9FBdT1vfBluA',
        prompt: 'You are a test investigative researcher agent.',
        knowledgeBase: []
      })
    });

    if (createAgentResponse2.ok) {
      const createResult2 = await createAgentResponse2.json();
      console.log(`   Second Creation Result: ${JSON.stringify(createResult2, null, 2)}`);
      
      if (createResult2.success) {
        console.log(`   ✅ Second agent creation call successful`);
        console.log(`   📝 Was Existing: ${createResult2.wasExisting}`);
        
        if (createResult2.wasExisting) {
          console.log(`   🎉 SUCCESS: Agent was properly reused instead of creating duplicate!`);
        } else {
          console.log(`   ⚠️  WARNING: Agent was created again instead of being reused!`);
        }
      } else {
        console.log(`   ❌ Second agent creation failed: ${createResult2.error}`);
      }
    } else {
      console.log(`   ❌ Second API call failed: ${createAgentResponse2.status} ${createAgentResponse2.statusText}`);
    }

    console.log('');
    console.log('🏁 Test completed!');
    console.log('=' .repeat(50));

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testPMOAgentDuplicateFix()
    .then(() => {
      console.log('✅ Test script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testPMOAgentDuplicateFix };
