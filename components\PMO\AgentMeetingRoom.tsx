"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../app/context/AuthContext';
import { Button } from '../ui/button';
import { toast } from '../ui/use-toast';
import { Toaster } from '../ui/toast';
import {
  FileText,
  Users,
  MessageSquare
} from 'lucide-react';
import { PMO_AGENT_VOICE_CONFIG, getPMOAgentConfig, generatePMOAgentId } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import { saveTranscript, extractTranscriptMetadata } from '../../lib/utils/transcriptUtils';
import AgentSelectionPanel from './AgentSelectionPanel';
import MeetingInterface from './MeetingInterface';
import DocumentContextPanel from './DocumentContextPanel';
import TranscriptPanel, { TranscriptMessage } from './TranscriptPanel';
import SaveTranscriptDialog from './SaveTranscriptDialog';
import QueueStatusModal from './QueueStatusModal';

interface AgentMeetingRoomProps {
  className?: string;
}

export interface MeetingState {
  isActive: boolean;
  selectedAgentType: string | null;
  agentId: string | null;
  isMuted: boolean;
  volume: number;
  isConnecting: boolean;
  connectionError: string | null;
}

export interface DocumentContext {
  documents: any[];
  selectedDocuments: string[];
  queuedDocuments: string[]; // Documents queued for upload
  isLoading: boolean;
}

export interface TranscriptState {
  dialogue: TranscriptMessage[];
  currentResponse: string;
  isSpeaking: boolean;
  isListening: boolean;
}

export default function AgentMeetingRoom({ className = '' }: AgentMeetingRoomProps) {
  const { user } = useAuth();
  
  // Meeting state
  const [meetingState, setMeetingState] = useState<MeetingState>({
    isActive: false,
    selectedAgentType: null,
    agentId: null,
    isMuted: false,
    volume: 0.8,
    isConnecting: false,
    connectionError: null
  });

  // Document context state
  const [documentContext, setDocumentContext] = useState<DocumentContext>({
    documents: [],
    selectedDocuments: [],
    queuedDocuments: [],
    isLoading: false
  });

  // UI state
  const [showAgentSelection, setShowAgentSelection] = useState(true);
  const [showDocumentPanel, setShowDocumentPanel] = useState(true);
  const [showTranscriptPanel, setShowTranscriptPanel] = useState(false);
  const [showSaveTranscriptDialog, setShowSaveTranscriptDialog] = useState(false);
  const [showQueueStatus, setShowQueueStatus] = useState(false);
  const [queueProcessingResults, setQueueProcessingResults] = useState<any[]>([]);
  const [currentProcessingDocument, setCurrentProcessingDocument] = useState<string | undefined>();

  // Transcript state
  const [transcriptState, setTranscriptState] = useState<TranscriptState>({
    dialogue: [],
    currentResponse: "",
    isSpeaking: false,
    isListening: false
  });

  /**
   * Handle transcript message updates
   */
  const handleTranscriptMessage = useCallback((message: string, isUser: boolean, agentName?: string) => {
    const newMessage: TranscriptMessage = {
      role: isUser ? "user" : (agentName || "agent"),
      content: message,
      timestamp: new Date()
    };

    setTranscriptState(prev => ({
      ...prev,
      dialogue: [...prev.dialogue, newMessage],
      currentResponse: "" // Clear current response when a complete message is added
    }));
  }, []);

  /**
   * Handle current response updates (streaming)
   */
  const handleCurrentResponse = useCallback((response: string) => {
    setTranscriptState(prev => ({
      ...prev,
      currentResponse: response
    }));
  }, []);

  /**
   * Handle speaking state updates
   */
  const handleSpeakingState = useCallback((isSpeaking: boolean, isListening: boolean) => {
    setTranscriptState(prev => ({
      ...prev,
      isSpeaking,
      isListening
    }));
  }, []);



  /**
   * Load documents for the selected agent type
   */
  const loadAgentDocuments = useCallback(async (agentType: string) => {
    if (!user?.email) return;

    setDocumentContext(prev => ({ ...prev, isLoading: true }));

    try {
      console.log(`[MEETING_ROOM] Loading documents for agent type: ${agentType}, user: ${user.email}`);

      // First try the global endpoint with agent type filter
      const globalResponse = await fetch(`/api/agent-outputs/global?userId=${encodeURIComponent(user.email)}&agentType=${encodeURIComponent(agentType)}`);

      if (globalResponse.ok) {
        const globalData = await globalResponse.json();
        console.log(`[MEETING_ROOM] Global endpoint response:`, globalData);

        if (globalData.success && globalData.outputs && globalData.outputs.length > 0) {
          setDocumentContext(prev => ({
            ...prev,
            documents: globalData.outputs,
            isLoading: false
          }));
          console.log(`[MEETING_ROOM] Loaded ${globalData.outputs.length} documents for ${agentType} from global endpoint`);
          return;
        }
      } else {
        console.warn(`[MEETING_ROOM] Global endpoint failed with status ${globalResponse.status}: ${globalResponse.statusText}`);
      }

      // Fallback: Use main agent outputs endpoint and filter client-side
      console.log(`[MEETING_ROOM] Falling back to main agent outputs endpoint`);
      const mainResponse = await fetch('/api/agent-outputs?page=1&limit=100');

      if (mainResponse.ok) {
        const mainData = await mainResponse.json();
        console.log(`[MEETING_ROOM] Main endpoint returned ${mainData.results?.length || 0} total documents`);

        if (mainData.results && mainData.results.length > 0) {
          // Filter documents by agent type (case-insensitive and flexible matching)
          const filteredDocuments = mainData.results.filter((doc: any) => {
            const docAgentType = doc.agentType || '';
            // Exact match
            if (docAgentType === agentType) return true;
            // Case-insensitive match
            if (docAgentType.toLowerCase() === agentType.toLowerCase()) return true;
            // Partial match for compound agent types
            if (docAgentType.toLowerCase().includes(agentType.toLowerCase()) ||
                agentType.toLowerCase().includes(docAgentType.toLowerCase())) return true;
            return false;
          });

          // Transform documents to match expected format
          const transformedDocuments = filteredDocuments.map((doc: any) => ({
            id: doc.id,
            title: doc.prompt?.substring(0, 100) || doc.title || 'Agent Output',
            content: doc.result?.output || doc.result?.message || doc.content || '',
            agentType: doc.agentType || agentType,
            createdAt: doc.timestamp || doc.createdAt || new Date().toISOString(),
            updatedAt: doc.updatedAt || doc.timestamp || doc.createdAt || new Date().toISOString(),
            metadata: doc.metadata || {},
            category: doc.category || doc.metadata?.category,
            fileUrl: doc.result?.documentUrl || doc.fileUrl,
            summary: doc.result?.summary || (doc.content || '').substring(0, 200) + '...',
            tags: doc.tags || [],
            status: doc.status,
            projectId: doc.projectId || doc.metadata?.pmoId,
            teamName: doc.metadata?.teamName,
            assignedTeam: doc.metadata?.assignedTeam
          }));

          setDocumentContext(prev => ({
            ...prev,
            documents: transformedDocuments,
            isLoading: false
          }));
          console.log(`[MEETING_ROOM] Loaded ${transformedDocuments.length} filtered documents for ${agentType} from main endpoint`);
          return;
        }
      } else {
        console.error(`[MEETING_ROOM] Main endpoint failed with status ${mainResponse.status}: ${mainResponse.statusText}`);
      }

      // If both endpoints fail or return no data
      console.warn(`[MEETING_ROOM] No documents found for agent type: ${agentType}`);
      setDocumentContext(prev => ({ ...prev, documents: [], isLoading: false }));

    } catch (error) {
      console.error('[MEETING_ROOM] Error loading documents:', error);
      setDocumentContext(prev => ({ ...prev, documents: [], isLoading: false }));
    }
  }, [user?.email]);

  /**
   * Handle agent selection
   */
  const handleAgentSelect = useCallback(async (agentType: string) => {
    if (!user?.email) return;

    console.log(`[MEETING_ROOM] Agent selected: ${agentType}`);

    setMeetingState(prev => ({
      ...prev,
      selectedAgentType: agentType,
      agentId: generatePMOAgentId(user.email!, agentType),
      connectionError: null
    }));

    setShowAgentSelection(false);

    // Load documents for the selected agent type
    await loadAgentDocuments(agentType);
  }, [user?.email, loadAgentDocuments]);

  /**
   * Start meeting with selected agent
   */
  const startMeeting = useCallback(async () => {
    if (!meetingState.selectedAgentType || !meetingState.agentId) return;

    setMeetingState(prev => ({ ...prev, isConnecting: true, connectionError: null }));

    try {
      console.log(`[MEETING_ROOM] Starting meeting with ${meetingState.selectedAgentType}`);

      // Step 1: Process queued documents if any
      if (documentContext.queuedDocuments.length > 0) {
        console.log(`[MEETING_ROOM] Processing ${documentContext.queuedDocuments.length} queued documents`);

        // Show queue status modal
        setShowQueueStatus(true);
        setQueueProcessingResults([]);

        const response = await fetch('/api/elevenlabs/process-queued-documents', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            queuedDocumentIds: documentContext.queuedDocuments,
            agentType: meetingState.selectedAgentType,
            forceUpload: false,
            forceReindex: false
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process queued documents');
        }

        const uploadResults = await response.json();
        console.log(`[MEETING_ROOM] Document processing results:`, uploadResults);

        // Update processing results
        setQueueProcessingResults(uploadResults.results || []);

        // Clear the queue after successful processing
        setDocumentContext(prev => ({
          ...prev,
          queuedDocuments: []
        }));
      }

      // Step 2: Initialize ElevenLabs conversation
      // TODO: Initialize ElevenLabs conversation
      // This will be implemented in the next step

      setMeetingState(prev => ({
        ...prev,
        isActive: true,
        isConnecting: false
      }));

    } catch (error) {
      console.error('[MEETING_ROOM] Error starting meeting:', error);
      setMeetingState(prev => ({
        ...prev,
        isConnecting: false,
        connectionError: error instanceof Error ? error.message : 'Failed to start meeting'
      }));
    }
  }, [meetingState.selectedAgentType, meetingState.agentId, documentContext.queuedDocuments]);

  /**
   * End meeting
   */
  const endMeeting = useCallback(() => {
    console.log('[MEETING_ROOM] Ending meeting');

    // Check if there's a transcript to save
    if (transcriptState.dialogue.length > 0) {
      setShowSaveTranscriptDialog(true);
    }

    setMeetingState(prev => ({
      ...prev,
      isActive: false,
      isConnecting: false,
      connectionError: null
    }));
  }, [transcriptState.dialogue.length]);

  /**
   * Toggle mute
   */
  const toggleMute = useCallback(() => {
    setMeetingState(prev => ({
      ...prev,
      isMuted: !prev.isMuted
    }));
  }, []);

  /**
   * Adjust volume
   */
  const adjustVolume = useCallback((volume: number) => {
    setMeetingState(prev => ({
      ...prev,
      volume: Math.max(0, Math.min(1, volume))
    }));
  }, []);

  /**
   * Reset to agent selection
   */
  const resetToAgentSelection = useCallback(() => {
    setMeetingState({
      isActive: false,
      selectedAgentType: null,
      agentId: null,
      isMuted: false,
      volume: 0.8,
      isConnecting: false,
      connectionError: null
    });
    setShowAgentSelection(true);
    setDocumentContext({
      documents: [],
      selectedDocuments: [],
      queuedDocuments: [],
      isLoading: false
    });
  }, []);

  // Get selected agent configuration
  const selectedAgentConfig = meetingState.selectedAgentType
    ? getPMOAgentConfig(meetingState.selectedAgentType)
    : null;

  /**
   * Handle save transcript from dialog
   */
  const handleSaveTranscriptFromDialog = useCallback(async (uploadToKnowledgeBase: boolean) => {
    if (!user?.email || transcriptState.dialogue.length === 0) {
      throw new Error('Cannot save: no user or empty transcript');
    }

    console.log('[TRANSCRIPT] Save transcript requested from dialog', {
      messageCount: transcriptState.dialogue.length,
      agentType: meetingState.selectedAgentType,
      agentName: selectedAgentConfig?.agentName,
      uploadToKnowledgeBase
    });

    // Show initial progress notification
    toast({
      title: "Saving Transcript",
      description: "Starting transcript save process...",
    });

    // Extract metadata
    const metadata = extractTranscriptMetadata(
      transcriptState.dialogue,
      selectedAgentConfig?.agentName || 'Agent',
      meetingState.selectedAgentType || 'PMO Agent',
      documentContext
    );

    // Save transcript with progress notifications
    const result = await saveTranscript(transcriptState.dialogue, {
      userId: user.email,
      agentId: meetingState.agentId || undefined,
      metadata,
      uploadToKnowledgeBase,
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        console.log(`[TRANSCRIPT] Progress: ${step} - ${message} (${progress}%)`);

        // Show specific progress notifications
        switch (step) {
          case 'pdf':
            toast({
              title: "Generating PDF",
              description: "Creating formatted PDF document...",
            });
            break;
          case 'upload':
            if (uploadToKnowledgeBase) {
              toast({
                title: "Uploading to Knowledge Base",
                description: message,
              });
            }
            break;
          case 'indexing':
            toast({
              title: "RAG Indexing",
              description: message,
            });
            break;
          case 'agent-update':
            toast({
              title: "Updating Agent",
              description: message,
            });
            break;
          case 'complete':
            toast({
              title: "Success!",
              description: `Transcript saved as ${result.fileName}${uploadToKnowledgeBase ? ' and added to knowledge base' : ''}`,
            });
            break;
          case 'error':
            toast({
              title: "Warning",
              description: message,
              variant: "destructive"
            });
            break;
        }
      }
    });

    if (!result.success) {
      toast({
        title: "Save Failed",
        description: result.error || 'Failed to save transcript',
        variant: "destructive"
      });
      throw new Error(result.error || 'Failed to save transcript');
    }

    console.log('[TRANSCRIPT] Transcript saved successfully from dialog', {
      fileName: result.fileName,
      pdfUrl: result.pdfUrl,
      knowledgeBaseId: result.knowledgeBaseId
    });

    // Final success notification
    toast({
      title: "Transcript Saved Successfully",
      description: `${result.fileName} has been saved${uploadToKnowledgeBase && result.knowledgeBaseId ? ' and indexed in knowledge base' : ''}`,
    });

    return result;
  }, [transcriptState.dialogue, user?.email, meetingState.selectedAgentType, meetingState.agentId, selectedAgentConfig?.agentName, documentContext]);

  /**
   * Save transcript as PDF (direct call)
   */
  const handleSaveTranscript = useCallback(async () => {
    if (!user?.email || transcriptState.dialogue.length === 0) {
      console.warn('[TRANSCRIPT] Cannot save: no user or empty transcript');
      toast({
        title: "Cannot Save Transcript",
        description: "No transcript content available to save",
        variant: "destructive"
      });
      return;
    }

    try {
      console.log('[TRANSCRIPT] Save transcript requested', {
        messageCount: transcriptState.dialogue.length,
        agentType: meetingState.selectedAgentType,
        agentName: selectedAgentConfig?.agentName
      });

      // Show initial notification
      toast({
        title: "Saving Transcript",
        description: "Starting transcript save process...",
      });

      // Extract metadata
      const metadata = extractTranscriptMetadata(
        transcriptState.dialogue,
        selectedAgentConfig?.agentName || 'Agent',
        meetingState.selectedAgentType || 'PMO Agent',
        documentContext
      );

      // Save transcript with progress notifications
      const result = await saveTranscript(transcriptState.dialogue, {
        userId: user.email,
        agentId: meetingState.agentId || undefined,
        metadata,
        uploadToKnowledgeBase: true,
        forceUpload: false,
        onProgress: (step: string, message: string) => {
          console.log(`[TRANSCRIPT] Progress: ${step} - ${message}`);

          // Show key progress notifications
          if (step === 'pdf') {
            toast({
              title: "Generating PDF",
              description: "Creating formatted PDF document...",
            });
          } else if (step === 'upload') {
            toast({
              title: "Uploading to Knowledge Base",
              description: message,
            });
          } else if (step === 'complete') {
            toast({
              title: "Success!",
              description: `Transcript saved as ${result.fileName} and added to knowledge base`,
            });
          }
        }
      });

      if (result.success) {
        console.log('[TRANSCRIPT] Transcript saved successfully', {
          fileName: result.fileName,
          pdfUrl: result.pdfUrl,
          knowledgeBaseId: result.knowledgeBaseId
        });

        toast({
          title: "Transcript Saved Successfully",
          description: `${result.fileName} has been saved and indexed in knowledge base`,
        });
      } else {
        console.error('[TRANSCRIPT] Failed to save transcript:', result.error);
        toast({
          title: "Save Failed",
          description: result.error || 'Failed to save transcript',
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('[TRANSCRIPT] Error in handleSaveTranscript:', error);
      toast({
        title: "Error",
        description: 'An unexpected error occurred while saving the transcript',
        variant: "destructive"
      });
    }
  }, [transcriptState.dialogue, user?.email, meetingState.selectedAgentType, meetingState.agentId, selectedAgentConfig?.agentName, documentContext]);

  return (
    <div className={`h-screen bg-gray-900 flex flex-col overflow-hidden ${className}`}>
      {/* Modern Header with Zoom-style design */}
      <div className="bg-gray-800 border-b border-gray-700 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-white">PMO Agent Meeting Room</h1>
                <p className="text-xs text-gray-400">
                  {selectedAgentConfig?.agentName || 'Select an agent to start'}
                </p>
              </div>
            </div>

            {selectedAgentConfig && (
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  meetingState.isActive ? 'bg-green-500' :
                  meetingState.isConnecting ? 'bg-yellow-500 animate-pulse' :
                  'bg-gray-500'
                }`}></div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${selectedAgentConfig.color}`}>
                  {selectedAgentConfig.agentName}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Transcript Panel Toggle */}
            {!showAgentSelection && (
              <Button
                variant={showTranscriptPanel ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowTranscriptPanel(!showTranscriptPanel);
                  // Hide documents panel when transcript is shown to prevent UI conflicts
                  if (!showTranscriptPanel) {
                    setShowDocumentPanel(false);
                  }
                }}
                className={`text-sm ${showTranscriptPanel ? 'bg-blue-600 hover:bg-blue-700' : 'text-gray-400 hover:text-white'}`}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Transcript
                {transcriptState.dialogue.length > 0 && (
                  <span className="ml-1 bg-blue-500 text-white text-xs rounded-full px-1.5 py-0.5">
                    {transcriptState.dialogue.length}
                  </span>
                )}
              </Button>
            )}

            {/* Document Panel Toggle */}
            {!showAgentSelection && (
              <Button
                variant={showDocumentPanel ? "default" : "ghost"}
                size="sm"
                onClick={() => {
                  setShowDocumentPanel(!showDocumentPanel);
                  // Hide transcript panel when documents is shown to prevent UI conflicts
                  if (!showDocumentPanel) {
                    setShowTranscriptPanel(false);
                  }
                }}
                className={`text-sm ${showDocumentPanel ? 'bg-purple-600 hover:bg-purple-700' : 'text-gray-400 hover:text-white'}`}
              >
                <FileText className="h-4 w-4 mr-2" />
                Documents
                {documentContext.selectedDocuments.length > 0 && (
                  <span className="ml-1 bg-purple-500 text-white text-xs rounded-full px-1.5 py-0.5">
                    {documentContext.selectedDocuments.length}
                  </span>
                )}
              </Button>
            )}

            {/* Change Agent Button */}
            {meetingState.selectedAgentType && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetToAgentSelection}
                className="text-gray-400 hover:text-white"
                disabled={meetingState.isActive || meetingState.isConnecting}
              >
                <Settings className="h-4 w-4 mr-2" />
                Change Agent
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Meeting Interface - Main Video Area */}
        <div className="flex-1 flex flex-col min-w-0">
          {showAgentSelection ? (
            <AgentSelectionPanel
              onAgentSelect={handleAgentSelect}
              availableAgents={Object.keys(PMO_AGENT_VOICE_CONFIG)}
              userId={user?.email}
              documentContext={documentContext.documents}
              selectedDocuments={documentContext.selectedDocuments}
              onLoadDocuments={loadAgentDocuments}
            />
          ) : (
            <MeetingInterface
              meetingState={meetingState}
              agentConfig={selectedAgentConfig}
              documentContext={documentContext}
              onStartMeeting={startMeeting}
              onEndMeeting={endMeeting}
              onToggleMute={toggleMute}
              onVolumeChange={adjustVolume}
              onTranscriptMessage={handleTranscriptMessage}
              onCurrentResponse={handleCurrentResponse}
              onSpeakingState={handleSpeakingState}
            />
          )}
        </div>

        {/* Transcript Panel - Sidebar */}
        {showTranscriptPanel && !showAgentSelection && (
          <div className="w-80 min-w-[320px] max-w-[500px] bg-gray-800 border-l border-gray-700 flex-shrink-0">
            <TranscriptPanel
              dialogue={transcriptState.dialogue}
              currentResponse={transcriptState.currentResponse}
              isSpeaking={transcriptState.isSpeaking}
              isListening={transcriptState.isListening}
              agentName={selectedAgentConfig?.agentName}
              onSaveTranscript={handleSaveTranscript}
            />
          </div>
        )}

        {/* Document Context Panel - Sidebar */}
        {showDocumentPanel && !showAgentSelection && (
          <div className="w-80 min-w-[320px] max-w-[500px] bg-gray-800 border-l border-gray-700 flex-shrink-0">
            <DocumentContextPanel
              documentContext={documentContext}
              agentType={meetingState.selectedAgentType}
              onDocumentSelect={(documentIds) => {
                setDocumentContext(prev => ({
                  ...prev,
                  selectedDocuments: documentIds
                }));
              }}
              queuedDocuments={documentContext.queuedDocuments}
              onQueueDocument={(documentId, action) => {
                setDocumentContext(prev => ({
                  ...prev,
                  queuedDocuments: action === 'add'
                    ? [...prev.queuedDocuments, documentId]
                    : prev.queuedDocuments.filter(id => id !== documentId)
                }));
              }}
            />
          </div>
        )}
      </div>

      {/* Queue Status Modal */}
      <QueueStatusModal
        isOpen={showQueueStatus}
        onClose={() => setShowQueueStatus(false)}
        queuedDocuments={documentContext.queuedDocuments}
        processingResults={queueProcessingResults}
        isProcessing={meetingState.isConnecting}
        currentDocument={currentProcessingDocument}
      />

      {/* Save Transcript Dialog */}
      <SaveTranscriptDialog
        isOpen={showSaveTranscriptDialog}
        onClose={() => setShowSaveTranscriptDialog(false)}
        onSave={handleSaveTranscriptFromDialog}
        dialogue={transcriptState.dialogue}
        agentName={selectedAgentConfig?.agentName || 'Agent'}
        agentType={meetingState.selectedAgentType || 'PMO Agent'}
        documentContext={documentContext}
      />

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}
